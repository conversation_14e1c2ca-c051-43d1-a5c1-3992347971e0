{"user": {"user_id": "user_b88554a0", "created_at": "2025-08-01T20:19:34.689099", "total_sessions": 22, "total_interactions": 45, "relationship_start": "2025-08-01T20:19:34.689136", "last_interaction": "2025-08-02T00:11:44.846981", "personality_profile": {}, "communication_preferences": {}, "learned_patterns": {}}, "memory_events": [{"event_id": "event_20250801_201951_d72e83", "timestamp": "2025-08-01T20:19:51.595835", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_201951_c713"}, "confidence": 0.95, "source_session": "session_20250801_201951_c713", "metadata": {}}, {"event_id": "event_20250801_202010_684f2a", "timestamp": "2025-08-01T20:20:10.431747", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_ca5b845a", "category": "personal_info", "key": "name", "value": "sarah", "confidence": 0.7, "first_mentioned": "2025-08-01T20:20:10.431639", "last_updated": "2025-08-01T20:20:10.431639", "source_sessions": ["session_20250801_201951_c713"], "historical_values": [], "verification_count": 1}}, "confidence": 0.7, "source_session": "session_20250801_201951_c713", "metadata": {}}, {"event_id": "event_20250801_202010_d73052", "timestamp": "2025-08-01T20:20:10.431978", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_207acb82", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_201951_c713"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_201951_c713", "metadata": {}}, {"event_id": "event_20250801_202010_ed5a57", "timestamp": "2025-08-01T20:20:10.432127", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T20:20:10.429952", "user_message": "Hi <PERSON>! I'm <PERSON>, a data scientist from Boston.", "ai_response": "Hello there! I'm here and ready to chat. What's on your mind?", "interaction_id": "int_bb587ded"}, "extracted_facts": 1, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_201951_c713", "metadata": {}}, {"event_id": "event_20250801_202104_598115", "timestamp": "2025-08-01T20:21:04.949153", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_201951_c713", "duration": "1 minutes", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_201951_c713", "metadata": {}}, {"event_id": "event_20250801_213016_name_change", "timestamp": "2025-08-01T21:30:16.621792", "memory_type": "semantic", "operation": "update", "content": {"fact_type": "name", "previous_value": "<PERSON>", "new_value": "Jordan", "category": "personal_info"}, "confidence": 0.95, "source_session": "session_20250801_201951_c713", "metadata": {"importance_score": 1.0, "context_type": "identity", "semantic_tags": ["identity", "name_change"]}}, {"event_id": "event_20250801_213116_job_add", "timestamp": "2025-08-01T21:31:16.774196", "memory_type": "semantic", "operation": "add", "content": {"fact_type": "occupation", "value": "frontend developer", "category": "professional"}, "confidence": 0.9, "source_session": "session_20250801_201951_c713", "metadata": {"importance_score": 0.8, "context_type": "professional", "semantic_tags": ["occupation", "frontend", "developer"]}}, {"event_id": "event_20250801_213216_pref_add", "timestamp": "2025-08-01T21:32:16.774196", "memory_type": "behavioral", "operation": "add", "content": {"fact_type": "response_style", "value": "detailed", "category": "preferences"}, "confidence": 0.9, "source_session": "session_20250801_201951_c713", "metadata": {"importance_score": 0.9, "context_type": "behavioral", "semantic_tags": ["preferences", "communication_style", "detailed_responses"]}}, {"event_id": "event_20250801_221201_41c9e6", "timestamp": "2025-08-01T22:12:01.264451", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_221201_8c95"}, "confidence": 0.95, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_221201_677332", "timestamp": "2025-08-01T22:12:01.266594", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_a3fe92ec", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_221201_aa0cb1", "timestamp": "2025-08-01T22:12:01.266692", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:12:01.264830", "user_message": "Hi <PERSON>, what's my name?", "ai_response": "Hello <PERSON>! How's your work as a frontend developer going today?", "interaction_id": "int_aadaed79"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_221201_ca648f", "timestamp": "2025-08-01T22:12:01.267045", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:12:01.266965", "user_message": "Tell me about machine learning", "ai_response": "Hello <PERSON>! How's your work as a frontend developer going today?", "interaction_id": "int_7c11aaa4"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_221201_9bfed7", "timestamp": "2025-08-01T22:12:01.270102", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_2d16d157", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.5, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_221201_301534", "timestamp": "2025-08-01T22:12:01.270252", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_221201_8c95", "duration": "0 seconds", "insights": {"session_summary": "Discussion about technology and learning with 2 exchanges", "topics_discussed": ["technology", "learning"], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_221201_8c95", "metadata": {}}, {"event_id": "event_20250801_225420_f68e28", "timestamp": "2025-08-01T22:54:20.894015", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_225420_8b84"}, "confidence": 0.95, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225421_9c6566", "timestamp": "2025-08-01T22:54:21.296808", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "Jordan", "new_value": "alex", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225421_7bfe27", "timestamp": "2025-08-01T22:54:21.297090", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_e66fd0df", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225421_b9103d", "timestamp": "2025-08-01T22:54:21.297239", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:54:21.294689", "user_message": "Hi <PERSON>! I'm <PERSON>, a backend developer.", "ai_response": "Hi <PERSON>! I think there might be some confusion, as I'm pretty sure we've had conversations before, and your name is actually <PERSON>, a frontend developer. Am I right?", "interaction_id": "int_058888ea"}, "extracted_facts": 1, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_a6f710", "timestamp": "2025-08-01T22:54:22.065707", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:54:22.065484", "user_message": "I work with Python and Django.", "ai_response": "Hi <PERSON>! It's great to hear that you're a frontend developer and work with Python and Django. I recall our previous conversations, and I'm impressed by your expertise in these technologies. How do you find the balance between building a robust frontend and ensuring seamless integration with the backend using Django? Do you have any favorite libraries or tools that you rely on for your projects?", "interaction_id": "int_6818f932"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_1e7866", "timestamp": "2025-08-01T22:54:22.432991", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_d5c13845", "category": "interests", "key": "preferences", "value": "concise answers unless i ask for details.", "confidence": 0.5599999999999999, "first_mentioned": "2025-08-01T22:54:22.432894", "last_updated": "2025-08-01T22:54:22.432894", "source_sessions": ["session_20250801_225420_8b84"], "historical_values": [], "verification_count": 1}}, "confidence": 0.5599999999999999, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_ab5699", "timestamp": "2025-08-01T22:54:22.433308", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_0be13de6", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.7, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_267842", "timestamp": "2025-08-01T22:54:22.433511", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:54:22.432776", "user_message": "I prefer concise answers unless I ask for details.", "ai_response": "<PERSON>, I remember you mentioning your preference for concise answers unless you ask for details. I'll keep that in mind and provide brief responses unless you ask for more information. How's your frontend development project going?", "interaction_id": "int_c7ec13f3"}, "extracted_facts": 1, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_7009e5", "timestamp": "2025-08-01T22:54:22.815949", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_654e9d6e", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225422_c2a010", "timestamp": "2025-08-01T22:54:22.816159", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:54:22.815809", "user_message": "What do you know about me so far?", "ai_response": "<PERSON>, it's great to catch up with you again! From our previous conversations, I recall that you're a frontend developer, and we've had a nice exchange about getting to know each other. I also remember that you've shared your preference for detailed responses, so I'll make sure to provide those for you. Additionally, I've noticed that you've had a name change in our conversations, from <PERSON> to <PERSON>, and then to the name you've consistently used with me, <PERSON>. How's your day going so far?", "interaction_id": "int_10cfa427"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225423_9399d9", "timestamp": "2025-08-01T22:54:23.334846", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_48405630", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.7, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225423_97e3d5", "timestamp": "2025-08-01T22:54:23.335093", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:54:23.334680", "user_message": "Tell me about neural networks in detail.", "ai_response": "<PERSON>! I'm happy to dive into the world of neural networks with you. As a frontend developer, you likely have a solid foundation in software development, and neural networks can be a fascinating topic to explore.\n\nNeural networks are a type of machine learning model inspired by the structure and function of the human brain. They're composed of layers of interconnected nodes or \"neurons,\" which process and transmit information. This architecture allows neural networks to recognize patterns, make predictions, and learn from data.\n\nA typical neural network consists of three primary layers: the input layer, hidden layers, and output layer. The input layer receives the input data, while the output layer produces the predicted output. The hidden layers, which can be multiple, are where the magic", "interaction_id": "int_ccd88640"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225423_977f1b", "timestamp": "2025-08-01T22:54:23.336583", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_d2c14ce3", "category": "response_preference", "description": "User prefers detailed responses", "trigger_conditions": ["response_type:detailed"], "preferred_response": "Provide detailed responses", "confidence": 0.4, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.4, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225423_c44995", "timestamp": "2025-08-01T22:54:23.336730", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_225420_8b84", "duration": "2 seconds", "insights": {"session_summary": "Discussion about technology and work with 5 exchanges", "topics_discussed": ["technology", "work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_225420_8b84", "metadata": {}}, {"event_id": "event_20250801_225908_d0f1f3", "timestamp": "2025-08-01T22:59:08.980112", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_225908_2636"}, "confidence": 0.95, "source_session": "session_20250801_225908_2636", "metadata": {}}, {"event_id": "event_20250801_225909_d66961", "timestamp": "2025-08-01T22:59:09.711609", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T22:59:09.708584", "user_message": "What's my name?", "ai_response": "I know exactly who you are, alex! Your name is, of course, alex, and you're a frontend developer. We've had a few conversations already, and I'm happy to chat with you again!", "interaction_id": "int_7e4ff5ff"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_225908_2636", "metadata": {}}, {"event_id": "event_20250801_225909_46cfbe", "timestamp": "2025-08-01T22:59:09.712139", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_c537bf97", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225908_2636"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_225908_2636", "metadata": {}}, {"event_id": "event_20250801_225909_557a46", "timestamp": "2025-08-01T22:59:09.712319", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_225908_2636", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_225908_2636", "metadata": {}}, {"event_id": "event_20250801_230001_a5693c", "timestamp": "2025-08-01T23:00:01.442824", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_230001_adb6"}, "confidence": 0.95, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230001_bf4dfd", "timestamp": "2025-08-01T23:00:01.919027", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_2ff8ad70", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230001_bcbe28", "timestamp": "2025-08-01T23:00:01.919317", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:00:01.914913", "user_message": "Hi <PERSON>!", "ai_response": "Hello again, alex! It's great to catch up with you again. I remember our previous conversations about technology and work. How's your frontend development journey been lately? Have you run into any exciting projects or challenges?", "interaction_id": "int_ed1d4e7d"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230002_3ee95d", "timestamp": "2025-08-01T23:00:02.212887", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:00:02.212790", "user_message": "What's my name?", "ai_response": "<PERSON>, I remember it's your name! We've had some great conversations so far, and I'm glad to recall that you're a frontend developer. How's your day going so far?", "interaction_id": "int_d0865d61"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230002_d05edc", "timestamp": "2025-08-01T23:00:02.704443", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "alex", "new_value": "actually", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230002_2296f7", "timestamp": "2025-08-01T23:00:02.704656", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:00:02.704274", "user_message": "I'm actually <PERSON>, a Python developer", "ai_response": "I think there may be a mistake here, alex! I'm quite certain we've had conversations before, and I remember you as a frontend developer, not a Python developer. Don't worry, it's easy to get things mixed up! Can you tell me more about what's changed in your career or interests that's led you to explore Python development now? I'm here to listen and help if I can!", "interaction_id": "int_f599faca"}, "extracted_facts": 1, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230003_a94955", "timestamp": "2025-08-01T23:00:03.400053", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_fe7d8ba6", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230003_e368dd", "timestamp": "2025-08-01T23:00:03.400240", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:00:03.399926", "user_message": "What do you know about me now?", "ai_response": "Actually, I remember that we've had 5 previous conversations, and we've been getting to know each other. You're a frontend developer, and I've learned that you prefer concise answers unless you ask for details. We've also had discussions about technology and work, and I've made sure to adapt my responses to your preferences. You've provided me with negative feedback in the past, which I've taken into account to adjust my approach. I've also learned that you appreciate detailed responses, so I'll make sure to provide comprehensive answers going forward. How can I assist you today?", "interaction_id": "int_4b1144b5"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230004_05cb46", "timestamp": "2025-08-01T23:00:04.746271", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_72f8c65d", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.7, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230004_a031f7", "timestamp": "2025-08-01T23:00:04.746452", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:00:04.746131", "user_message": "Tell me about machine learning in detail", "ai_response": "actually, I'm excited to dive into the world of machine learning with you! Since our previous conversations, I've learned that you're a frontend developer, and I'm assuming you have a solid foundation in programming and software development.\n\nMachine learning is a subset of artificial intelligence that involves training algorithms to make predictions or take actions based on data. The primary goal is to enable computers to learn from experience, without being explicitly programmed. This is achieved by analyzing patterns and relationships within the data, which allows the algorithm to improve its performance over time.\n\nThere are three primary types of machine learning:\n\n1. **Supervised Learning**: In this approach, the algorithm is trained on labeled data, where the correct output is already known. The goal is to learn a", "interaction_id": "int_45090129"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230004_0a5f69", "timestamp": "2025-08-01T23:00:04.747509", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_9af7c7ab", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.4, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.4, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230004_f33108", "timestamp": "2025-08-01T23:00:04.747641", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_230001_adb6", "duration": "3 seconds", "insights": {"session_summary": "Discussion about technology and learning with 5 exchanges", "topics_discussed": ["technology", "learning"], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_230001_adb6", "metadata": {}}, {"event_id": "event_20250801_230729_name_change", "timestamp": "2025-08-01T23:07:29.634024", "memory_type": "semantic", "operation": "update", "content": {"fact_type": "name", "previous_value": "Jordan", "new_value": "<PERSON>", "category": "personal_info", "user_statement": "MY new name is rich"}, "confidence": 0.95, "source_session": "session_39442724", "metadata": {"importance_score": 1.0, "context_type": "identity", "semantic_tags": ["identity", "name_change", "explicit_correction"], "user_initiated": true}}, {"event_id": "event_20250801_232434_4936ff", "timestamp": "2025-08-01T23:24:34.343872", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_232434_875a"}, "confidence": 0.95, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_c7ab90", "timestamp": "2025-08-01T23:24:34.347403", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.344180", "user_message": "What's my name?", "ai_response": "Your name is <PERSON>!", "interaction_id": "int_6cedc209"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_147823", "timestamp": "2025-08-01T23:24:34.347797", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "<PERSON>", "new_value": "sarah", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_3c0632", "timestamp": "2025-08-01T23:24:34.347913", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.347657", "user_message": "MY new name is <PERSON>", "ai_response": "I'm listening carefully, <PERSON>. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_149e2070"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_b5c463", "timestamp": "2025-08-01T23:24:34.348305", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_e8045b17", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_367006", "timestamp": "2025-08-01T23:24:34.348409", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.348190", "user_message": "What's my name now?", "ai_response": "Your name is sarah!", "interaction_id": "int_7b3a9020"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_1e4a98", "timestamp": "2025-08-01T23:24:34.348786", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "sarah", "new_value": "<PERSON>", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_0d8094", "timestamp": "2025-08-01T23:24:34.348918", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "<PERSON>", "new_value": "mike", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_f23021", "timestamp": "2025-08-01T23:24:34.349038", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "mike", "new_value": "mike from", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_080f8e", "timestamp": "2025-08-01T23:24:34.349305", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_12837096", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_354333", "timestamp": "2025-08-01T23:24:34.349421", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.348664", "user_message": "Actually, call me <PERSON> from now on", "ai_response": "I'm listening carefully, sarah. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_1c3d26fe"}, "extracted_facts": 3, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_42c37f", "timestamp": "2025-08-01T23:24:34.349907", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.349829", "user_message": "What do you remember about my name?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_8f399aa3"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_54fa6c", "timestamp": "2025-08-01T23:24:34.350274", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "mike from", "new_value": "sarah", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_51436a", "timestamp": "2025-08-01T23:24:34.350368", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "sarah", "new_value": "<PERSON>", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_ea70a9", "timestamp": "2025-08-01T23:24:34.350453", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "<PERSON>", "new_value": "mike", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_72d91f", "timestamp": "2025-08-01T23:24:34.350570", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "mike", "new_value": "mike from", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_fc2094", "timestamp": "2025-08-01T23:24:34.350777", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_21914b80", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.6, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.6, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_0399de", "timestamp": "2025-08-01T23:24:34.350911", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_232434_875a", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 5 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.30000000000000004, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_232434_875a", "metadata": {}}, {"event_id": "event_20250801_232434_0d9987", "timestamp": "2025-08-01T23:24:34.372499", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_232434_ff41"}, "confidence": 0.95, "source_session": "session_20250801_232434_ff41", "metadata": {}}, {"event_id": "event_20250801_232434_926f46", "timestamp": "2025-08-01T23:24:34.373020", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_0215cf95", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_232434_ff41", "metadata": {}}, {"event_id": "event_20250801_232434_0538b0", "timestamp": "2025-08-01T23:24:34.373162", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.372815", "user_message": "Hi <PERSON>, what's my name?", "ai_response": "Hello mike from! How's your work as a frontend developer going today?", "interaction_id": "int_d6ff86ec"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_232434_ff41", "metadata": {}}, {"event_id": "event_20250801_232434_948d6a", "timestamp": "2025-08-01T23:24:34.373415", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_8f33397d", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_232434_ff41", "metadata": {}}, {"event_id": "event_20250801_232434_97a152", "timestamp": "2025-08-01T23:24:34.373589", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_232434_ff41", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_232434_ff41", "metadata": {}}, {"event_id": "event_20250801_232434_5e7239", "timestamp": "2025-08-01T23:24:34.392340", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_232434_9df9"}, "confidence": 0.95, "source_session": "session_20250801_232434_9df9", "metadata": {}}, {"event_id": "event_20250801_232434_fac7e5", "timestamp": "2025-08-01T23:24:34.392738", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.392622", "user_message": "Do you remember my name?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_3b9c1d4b"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_9df9", "metadata": {}}, {"event_id": "event_20250801_232434_31ee2d", "timestamp": "2025-08-01T23:24:34.393031", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_232434_9df9", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_232434_9df9", "metadata": {}}, {"event_id": "event_20250801_232434_810085", "timestamp": "2025-08-01T23:24:34.410227", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_232434_77a9"}, "confidence": 0.95, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_a9a368", "timestamp": "2025-08-01T23:24:34.410798", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.410700", "user_message": "What did we discuss about React vs Angular?", "ai_response": "I'm listening carefully, mike from. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_2e6fec80"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_6f2b3b", "timestamp": "2025-08-01T23:24:34.411200", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.411112", "user_message": "Remember when we talked about machine learning?", "ai_response": "Hello mike from! How's your work as a frontend developer going today?", "interaction_id": "int_b34e0812"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_302586", "timestamp": "2025-08-01T23:24:34.411506", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.411441", "user_message": "What was that project I mentioned last time?", "ai_response": "I'm listening carefully, mike from. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_c11a6702"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_53bd09", "timestamp": "2025-08-01T23:24:34.411895", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:24:34.411819", "user_message": "Do you recall our conversation about Python frameworks?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_5b693d9a"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_ce892e", "timestamp": "2025-08-01T23:24:34.412314", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_8d270c98", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.75, "learned_from_sessions": ["session_20250801_232434_77a9"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.75, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232434_b4d5ba", "timestamp": "2025-08-01T23:24:34.412437", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_232434_77a9", "duration": "0 seconds", "insights": {"session_summary": "Multi-topic discussion covering technology, work, learning and more with 4 exchanges", "topics_discussed": ["technology", "work", "learning"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250801_232434_77a9", "metadata": {}}, {"event_id": "event_20250801_232614_ffb810", "timestamp": "2025-08-01T23:26:14.876914", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250801_232614_409a"}, "confidence": 0.95, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_346483", "timestamp": "2025-08-01T23:26:14.882042", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:26:14.877722", "user_message": "what my name", "ai_response": "Your name is <PERSON>!", "interaction_id": "int_823bd67a"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_786ce9", "timestamp": "2025-08-01T23:26:14.882532", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "<PERSON>", "new_value": "rich", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_52c6da", "timestamp": "2025-08-01T23:26:14.882697", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:26:14.882353", "user_message": "MY new name is rich", "ai_response": "I'm listening carefully, <PERSON>. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_33e3de73"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_da0ca3", "timestamp": "2025-08-01T23:26:14.915767", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:26:14.915517", "user_message": "what my name", "ai_response": "Your name is rich!", "interaction_id": "int_86df72f7"}, "extracted_facts": 0, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_e5d84b", "timestamp": "2025-08-01T23:26:14.916215", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_c4a6496b", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.8, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_f24f84", "timestamp": "2025-08-01T23:26:14.916347", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-01T23:26:14.916018", "user_message": "Hi <PERSON>!", "ai_response": "Hello rich! How's your work as a frontend developer going today?", "interaction_id": "int_16524e92"}, "extracted_facts": 0, "behavioral_updates": 1}, "confidence": 0.85, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_80e011", "timestamp": "2025-08-01T23:26:14.917040", "memory_type": "behavioral", "operation": "add", "content": {"rule": {"rule_id": "rule_c803b846", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}}, "confidence": 0.5, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250801_232614_1ed009", "timestamp": "2025-08-01T23:26:14.917228", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250801_232614_409a", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 4 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.6, "factors": ["High user engagement"]}}}, "confidence": 0.85, "source_session": "session_20250801_232614_409a", "metadata": {}}, {"event_id": "event_20250802_001137_dc1cbf", "timestamp": "2025-08-02T00:11:37.687168", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_4044"}, "confidence": 0.95, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_0432ff", "timestamp": "2025-08-02T00:11:37.691562", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_a04dc986", "category": "professional", "key": "company", "value": "Openai", "confidence": 0.95, "first_mentioned": "2025-08-02T00:11:37.691455", "last_updated": "2025-08-02T00:11:37.691455", "source_sessions": ["session_20250802_001137_4044"], "historical_values": [], "verification_count": 1}}, "confidence": 0.95, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_5235ef", "timestamp": "2025-08-02T00:11:37.691712", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Openai", "new_value": "openai", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_1e03c7", "timestamp": "2025-08-02T00:11:37.691808", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.687420", "user_message": "I got a job at OpenAI. Kind of crazy.", "ai_response": "I'm listening carefully, rich. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_1fc80eea"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_0892af", "timestamp": "2025-08-02T00:11:37.692165", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "openai", "new_value": "Openai", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_176ac8", "timestamp": "2025-08-02T00:11:37.692280", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Openai", "new_value": "openai", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_2dece6", "timestamp": "2025-08-02T00:11:37.692408", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_4044", "duration": "0 seconds", "insights": {"session_summary": "Discussion about technology and work with 1 exchanges", "topics_discussed": ["technology", "work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001137_4044", "metadata": {}}, {"event_id": "event_20250802_001137_1e6fe2", "timestamp": "2025-08-02T00:11:37.719231", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_d8bd"}, "confidence": 0.95, "source_session": "session_20250802_001137_d8bd", "metadata": {}}, {"event_id": "event_20250802_001137_8799d4", "timestamp": "2025-08-02T00:11:37.720059", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "rich", "new_value": "max", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_d8bd", "metadata": {}}, {"event_id": "event_20250802_001137_9b4a34", "timestamp": "2025-08-02T00:11:37.720262", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.719808", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, rich. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_20bd7fa8"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_d8bd", "metadata": {}}, {"event_id": "event_20250802_001137_05a534", "timestamp": "2025-08-02T00:11:37.720872", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_d8bd", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001137_d8bd", "metadata": {}}, {"event_id": "event_20250802_001137_75e3aa", "timestamp": "2025-08-02T00:11:37.745491", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_0475"}, "confidence": 0.95, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_86012f", "timestamp": "2025-08-02T00:11:37.746429", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_7a428d84", "category": "professional", "key": "previous_company", "value": "Meta", "confidence": 0.9, "first_mentioned": "2025-08-02T00:11:37.746280", "last_updated": "2025-08-02T00:11:37.746280", "source_sessions": ["session_20250802_001137_0475"], "historical_values": [], "verification_count": 1}}, "confidence": 0.9, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_c770f2", "timestamp": "2025-08-02T00:11:37.746821", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Meta", "new_value": "meta", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_8ddc7c", "timestamp": "2025-08-02T00:11:37.747134", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.746051", "user_message": "I used to work at Meta", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_75f75165"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_bc04c8", "timestamp": "2025-08-02T00:11:37.747995", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "meta", "new_value": "Meta", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_81c51b", "timestamp": "2025-08-02T00:11:37.748292", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Meta", "new_value": "meta", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_0609c9", "timestamp": "2025-08-02T00:11:37.748490", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_0475", "duration": "0 seconds", "insights": {"session_summary": "Discussion about work with 1 exchanges", "topics_discussed": ["work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001137_0475", "metadata": {}}, {"event_id": "event_20250802_001137_a88fd6", "timestamp": "2025-08-02T00:11:37.779328", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_fab8"}, "confidence": 0.95, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_231a7b", "timestamp": "2025-08-02T00:11:37.780391", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_7c306ac3", "category": "preferences", "key": "boundaries", "value": "no_personal_questions", "confidence": 0.95, "first_mentioned": "2025-08-02T00:11:37.780280", "last_updated": "2025-08-02T00:11:37.780280", "source_sessions": ["session_20250802_001137_fab8"], "historical_values": [], "verification_count": 1}}, "confidence": 0.95, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_5f9284", "timestamp": "2025-08-02T00:11:37.780608", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "conditional_topics", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_6840be", "timestamp": "2025-08-02T00:11:37.780817", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "conditional_topics", "new_value": "don't ask me personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_1346d2", "timestamp": "2025-08-02T00:11:37.780980", "memory_type": "semantic", "operation": "add", "content": {"fact": {"fact_id": "fact_114ba4bf", "category": "preferences", "key": "interaction_style", "value": "personal questions", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.780920", "last_updated": "2025-08-02T00:11:37.780920", "source_sessions": ["session_20250802_001137_fab8"], "historical_values": [], "verification_count": 1}}, "confidence": 0.7, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_4b10d7", "timestamp": "2025-08-02T00:11:37.781129", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.779915", "user_message": "don't ask me personal questions unless I bring it up first", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_c9992e1e"}, "extracted_facts": 4, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_8ef882", "timestamp": "2025-08-02T00:11:37.781831", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask me personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_3e3236", "timestamp": "2025-08-02T00:11:37.781981", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "conditional_topics", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_6682d6", "timestamp": "2025-08-02T00:11:37.782150", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "conditional_topics", "new_value": "don't ask me personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_575771", "timestamp": "2025-08-02T00:11:37.782352", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_fab8", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001137_fab8", "metadata": {}}, {"event_id": "event_20250802_001137_80f818", "timestamp": "2025-08-02T00:11:37.811226", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_62b2"}, "confidence": 0.95, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_afc87d", "timestamp": "2025-08-02T00:11:37.812032", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "openai", "new_value": "Google", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_cf02e3", "timestamp": "2025-08-02T00:11:37.812178", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Google", "new_value": "google", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_7aa77d", "timestamp": "2025-08-02T00:11:37.812301", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.811802", "user_message": "I got a job at Google", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_181c88fb"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_4191c7", "timestamp": "2025-08-02T00:11:37.812547", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "max", "new_value": "alex", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_8fc8ea", "timestamp": "2025-08-02T00:11:37.812745", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.812414", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_da23a746"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_a1abe2", "timestamp": "2025-08-02T00:11:37.813013", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "meta", "new_value": "Apple", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_b3fee8", "timestamp": "2025-08-02T00:11:37.813180", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Apple", "new_value": "apple", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_9d6793", "timestamp": "2025-08-02T00:11:37.813334", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.812894", "user_message": "I used to work at Apple", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_b332274c"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_eca21c", "timestamp": "2025-08-02T00:11:37.813742", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask me personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_46ef03", "timestamp": "2025-08-02T00:11:37.813932", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "don't ask personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_f8bbe9", "timestamp": "2025-08-02T00:11:37.814070", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:37.813530", "user_message": "don't ask personal questions", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_8cc52c20"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_eed2cc", "timestamp": "2025-08-02T00:11:37.814761", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "google", "new_value": "Google", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_4e8e40", "timestamp": "2025-08-02T00:11:37.814917", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Google", "new_value": "google", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_f672c0", "timestamp": "2025-08-02T00:11:37.815090", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "apple", "new_value": "Apple", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_39038b", "timestamp": "2025-08-02T00:11:37.815219", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Apple", "new_value": "apple", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_ea62f3", "timestamp": "2025-08-02T00:11:37.815372", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_0fe88f", "timestamp": "2025-08-02T00:11:37.815511", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "don't ask personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_9f3bfd", "timestamp": "2025-08-02T00:11:37.815662", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_62b2", "duration": "0 seconds", "insights": {"session_summary": "Discussion about work with 4 exchanges", "topics_discussed": ["work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001137_62b2", "metadata": {}}, {"event_id": "event_20250802_001137_520597", "timestamp": "2025-08-02T00:11:37.842318", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001137_f4b0"}, "confidence": 0.95, "source_session": "session_20250802_001137_f4b0", "metadata": {}}, {"event_id": "event_20250802_001137_771f53", "timestamp": "2025-08-02T00:11:37.843239", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001137_f4b0", "duration": "0 seconds", "insights": {"session_summary": "Empty session", "topics_discussed": [], "mood_indicators": [], "user_satisfaction": null, "key_insights": []}}, "confidence": 0.85, "source_session": "session_20250802_001137_f4b0", "metadata": {}}, {"event_id": "event_20250802_001144_d641ed", "timestamp": "2025-08-02T00:11:44.697799", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_cef9"}, "confidence": 0.95, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_75aa25", "timestamp": "2025-08-02T00:11:44.703533", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "google", "new_value": "Openai", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_087640", "timestamp": "2025-08-02T00:11:44.703750", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Openai", "new_value": "openai", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_480bb3", "timestamp": "2025-08-02T00:11:44.703915", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.698386", "user_message": "I got a job at OpenAI. Kind of crazy.", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_70567f11"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_1d8ea7", "timestamp": "2025-08-02T00:11:44.704495", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "openai", "new_value": "Openai", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_adfae1", "timestamp": "2025-08-02T00:11:44.704631", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Openai", "new_value": "openai", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_098ec8", "timestamp": "2025-08-02T00:11:44.704786", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_cef9", "duration": "0 seconds", "insights": {"session_summary": "Discussion about technology and work with 1 exchanges", "topics_discussed": ["technology", "work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001144_cef9", "metadata": {}}, {"event_id": "event_20250802_001144_d99583", "timestamp": "2025-08-02T00:11:44.735197", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_8b2b"}, "confidence": 0.95, "source_session": "session_20250802_001144_8b2b", "metadata": {}}, {"event_id": "event_20250802_001144_d1a88b", "timestamp": "2025-08-02T00:11:44.735737", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "alex", "new_value": "max", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_8b2b", "metadata": {}}, {"event_id": "event_20250802_001144_3e9f2b", "timestamp": "2025-08-02T00:11:44.735859", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.735600", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_0ab12a90"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_8b2b", "metadata": {}}, {"event_id": "event_20250802_001144_0e0532", "timestamp": "2025-08-02T00:11:44.736314", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_8b2b", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001144_8b2b", "metadata": {}}, {"event_id": "event_20250802_001144_b7cc54", "timestamp": "2025-08-02T00:11:44.759016", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_9e11"}, "confidence": 0.95, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_7196b6", "timestamp": "2025-08-02T00:11:44.759637", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "apple", "new_value": "Meta", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_b2bce6", "timestamp": "2025-08-02T00:11:44.759777", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Meta", "new_value": "meta", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_5c41d8", "timestamp": "2025-08-02T00:11:44.759907", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.759417", "user_message": "I used to work at Meta", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_2da68674"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_79d231", "timestamp": "2025-08-02T00:11:44.760366", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "meta", "new_value": "Meta", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_1b15f8", "timestamp": "2025-08-02T00:11:44.760491", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Meta", "new_value": "meta", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_2ce76d", "timestamp": "2025-08-02T00:11:44.760637", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_9e11", "duration": "0 seconds", "insights": {"session_summary": "Discussion about work with 1 exchanges", "topics_discussed": ["work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001144_9e11", "metadata": {}}, {"event_id": "event_20250802_001144_a7ff37", "timestamp": "2025-08-02T00:11:44.786822", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_3607"}, "confidence": 0.95, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_5ea9b5", "timestamp": "2025-08-02T00:11:44.787486", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_ee2e5c", "timestamp": "2025-08-02T00:11:44.787597", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "conditional_topics", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_5e8f89", "timestamp": "2025-08-02T00:11:44.787711", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "conditional_topics", "new_value": "don't ask me personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_f5cca1", "timestamp": "2025-08-02T00:11:44.787835", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.787294", "user_message": "don't ask me personal questions unless I bring it up first", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_22564ddd"}, "extracted_facts": 4, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_a45158", "timestamp": "2025-08-02T00:11:44.788205", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask me personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_67e986", "timestamp": "2025-08-02T00:11:44.788332", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "conditional_topics", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_5be578", "timestamp": "2025-08-02T00:11:44.788478", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "conditional_topics", "new_value": "don't ask me personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_06c346", "timestamp": "2025-08-02T00:11:44.788635", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_3607", "duration": "0 seconds", "insights": {"session_summary": "General conversation with 1 exchanges", "topics_discussed": [], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001144_3607", "metadata": {}}, {"event_id": "event_20250802_001144_9d8e85", "timestamp": "2025-08-02T00:11:44.815343", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_ba5f"}, "confidence": 0.95, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_e4b422", "timestamp": "2025-08-02T00:11:44.815864", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "openai", "new_value": "Google", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_acd6c0", "timestamp": "2025-08-02T00:11:44.815986", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Google", "new_value": "google", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_ea5135", "timestamp": "2025-08-02T00:11:44.816104", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.815703", "user_message": "I got a job at Google", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_54f6015a"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_3f6d57", "timestamp": "2025-08-02T00:11:44.816413", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_ca5b845a", "old_value": "max", "new_value": "alex", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_9b907a", "timestamp": "2025-08-02T00:11:44.816566", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.816194", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_bdf1ac0b"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_d5deb6", "timestamp": "2025-08-02T00:11:44.816837", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "meta", "new_value": "Apple", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_f80683", "timestamp": "2025-08-02T00:11:44.816957", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Apple", "new_value": "apple", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_919972", "timestamp": "2025-08-02T00:11:44.817074", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.816677", "user_message": "I used to work at Apple", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_86a3b66e"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_36d7b8", "timestamp": "2025-08-02T00:11:44.817368", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask me personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_9581f6", "timestamp": "2025-08-02T00:11:44.817501", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "don't ask personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_71d7c9", "timestamp": "2025-08-02T00:11:44.817626", "memory_type": "episodic", "operation": "add", "content": {"interaction": {"timestamp": "2025-08-02T00:11:44.817185", "user_message": "don't ask personal questions", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_5210e088"}, "extracted_facts": 2, "behavioral_updates": 0}, "confidence": 0.85, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_9c9f99", "timestamp": "2025-08-02T00:11:44.818144", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "google", "new_value": "Google", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_8b6df3", "timestamp": "2025-08-02T00:11:44.818297", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_a04dc986", "old_value": "Google", "new_value": "google", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_89b569", "timestamp": "2025-08-02T00:11:44.818442", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "apple", "new_value": "Apple", "confidence": 0.9}, "confidence": 0.9, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_06692f", "timestamp": "2025-08-02T00:11:44.818562", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7a428d84", "old_value": "Apple", "new_value": "apple", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_4afe9d", "timestamp": "2025-08-02T00:11:44.818694", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "don't ask personal questions", "new_value": "no_personal_questions", "confidence": 0.95}, "confidence": 0.95, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_653ff2", "timestamp": "2025-08-02T00:11:44.818809", "memory_type": "semantic", "operation": "update", "content": {"fact_id": "fact_7c306ac3", "old_value": "no_personal_questions", "new_value": "don't ask personal questions", "confidence": 0.7}, "confidence": 0.7, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_8c54e1", "timestamp": "2025-08-02T00:11:44.818945", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_ba5f", "duration": "0 seconds", "insights": {"session_summary": "Discussion about work with 4 exchanges", "topics_discussed": ["work"], "mood_indicators": ["neutral"], "user_satisfaction": 0.5, "key_insights": ["Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}}, "confidence": 0.85, "source_session": "session_20250802_001144_ba5f", "metadata": {}}, {"event_id": "event_20250802_001144_aac2b2", "timestamp": "2025-08-02T00:11:44.846335", "memory_type": "episodic", "operation": "add", "content": {"action": "session_start", "session_id": "session_20250802_001144_b275"}, "confidence": 0.95, "source_session": "session_20250802_001144_b275", "metadata": {}}, {"event_id": "event_20250802_001144_5f52ed", "timestamp": "2025-08-02T00:11:44.847026", "memory_type": "episodic", "operation": "add", "content": {"action": "session_end", "session_id": "session_20250802_001144_b275", "duration": "0 seconds", "insights": {"session_summary": "Empty session", "topics_discussed": [], "mood_indicators": [], "user_satisfaction": null, "key_insights": []}}, "confidence": 0.85, "source_session": "session_20250802_001144_b275", "metadata": {}}], "sessions": {"session_20250801_201951_c713": {"session_id": "session_20250801_201951_c713", "start_time": "2025-08-01T21:25:30.747489", "end_time": "2025-08-01T21:33:16.774196", "message_count": 4, "conversation_log": [{"timestamp": "2025-08-01T21:25:30.747489", "user_message": "Hi <PERSON>! I'm <PERSON>.", "ai_response": "Hello <PERSON>! Nice to meet you. What would you like to talk about?", "interaction_id": "int_001"}, {"timestamp": "2025-08-01T21:30:16.621792", "user_message": "Hey, I changed my name to <PERSON> now. Just use that from now on.", "ai_response": "Got it, <PERSON>! I've updated my memory with your new name. How can I help you today?", "interaction_id": "int_002"}, {"timestamp": "2025-08-01T21:31:16.774196", "user_message": "I'm a frontend developer.", "ai_response": "That's great, Jordan! Frontend development is such an exciting field. What kind of projects do you work on?", "interaction_id": "int_003"}, {"timestamp": "2025-08-01T21:32:16.774196", "user_message": "From now on, when I ask for information, always give me detailed answers.", "ai_response": "Absolutely, <PERSON>! I'll make sure to provide detailed, comprehensive responses when you ask for information. I've noted this preference in my memory.", "interaction_id": "int_004"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "User introduced themselves as <PERSON>, then changed name to <PERSON>, shared profession as frontend developer, and set preference for detailed responses", "mood_indicators": ["neutral", "informative"], "topics_discussed": ["name change", "frontend development", "user preferences", "personal identity"], "user_satisfaction": 0.4, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250801_221201_8c95": {"session_id": "session_20250801_221201_8c95", "start_time": "2025-08-01T22:12:01.264395", "end_time": "2025-08-01T22:12:01.269837", "message_count": 2, "conversation_log": [{"timestamp": "2025-08-01T22:12:01.264830", "user_message": "Hi <PERSON>, what's my name?", "ai_response": "Hello <PERSON>! How's your work as a frontend developer going today?", "interaction_id": "int_aadaed79"}, {"timestamp": "2025-08-01T22:12:01.266965", "user_message": "Tell me about machine learning", "ai_response": "Hello <PERSON>! How's your work as a frontend developer going today?", "interaction_id": "int_7c11aaa4"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about technology and learning with 2 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "learning"], "user_satisfaction": 0.4, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}, "session_20250801_225420_8b84": {"session_id": "session_20250801_225420_8b84", "start_time": "2025-08-01T22:54:20.893961", "end_time": "2025-08-01T22:54:23.336209", "message_count": 5, "conversation_log": [{"timestamp": "2025-08-01T22:54:21.294689", "user_message": "Hi <PERSON>! I'm <PERSON>, a backend developer.", "ai_response": "Hi <PERSON>! I think there might be some confusion, as I'm pretty sure we've had conversations before, and your name is actually <PERSON>, a frontend developer. Am I right?", "interaction_id": "int_058888ea"}, {"timestamp": "2025-08-01T22:54:22.065484", "user_message": "I work with Python and Django.", "ai_response": "Hi <PERSON>! It's great to hear that you're a frontend developer and work with Python and Django. I recall our previous conversations, and I'm impressed by your expertise in these technologies. How do you find the balance between building a robust frontend and ensuring seamless integration with the backend using Django? Do you have any favorite libraries or tools that you rely on for your projects?", "interaction_id": "int_6818f932"}, {"timestamp": "2025-08-01T22:54:22.432776", "user_message": "I prefer concise answers unless I ask for details.", "ai_response": "<PERSON>, I remember you mentioning your preference for concise answers unless you ask for details. I'll keep that in mind and provide brief responses unless you ask for more information. How's your frontend development project going?", "interaction_id": "int_c7ec13f3"}, {"timestamp": "2025-08-01T22:54:22.815809", "user_message": "What do you know about me so far?", "ai_response": "<PERSON>, it's great to catch up with you again! From our previous conversations, I recall that you're a frontend developer, and we've had a nice exchange about getting to know each other. I also remember that you've shared your preference for detailed responses, so I'll make sure to provide those for you. Additionally, I've noticed that you've had a name change in our conversations, from <PERSON> to <PERSON>, and then to the name you've consistently used with me, <PERSON>. How's your day going so far?", "interaction_id": "int_10cfa427"}, {"timestamp": "2025-08-01T22:54:23.334680", "user_message": "Tell me about neural networks in detail.", "ai_response": "<PERSON>! I'm happy to dive into the world of neural networks with you. As a frontend developer, you likely have a solid foundation in software development, and neural networks can be a fascinating topic to explore.\n\nNeural networks are a type of machine learning model inspired by the structure and function of the human brain. They're composed of layers of interconnected nodes or \"neurons,\" which process and transmit information. This architecture allows neural networks to recognize patterns, make predictions, and learn from data.\n\nA typical neural network consists of three primary layers: the input layer, hidden layers, and output layer. The input layer receives the input data, while the output layer produces the predicted output. The hidden layers, which can be multiple, are where the magic", "interaction_id": "int_ccd88640"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about technology and work with 5 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "work"], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}, "session_20250801_225908_2636": {"session_id": "session_20250801_225908_2636", "start_time": "2025-08-01T22:59:08.980063", "end_time": "2025-08-01T22:59:09.711876", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-01T22:59:09.708584", "user_message": "What's my name?", "ai_response": "I know exactly who you are, alex! Your name is, of course, alex, and you're a frontend developer. We've had a few conversations already, and I'm happy to chat with you again!", "interaction_id": "int_7e4ff5ff"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}, "session_20250801_230001_adb6": {"session_id": "session_20250801_230001_adb6", "start_time": "2025-08-01T23:00:01.442770", "end_time": "2025-08-01T23:00:04.747199", "message_count": 5, "conversation_log": [{"timestamp": "2025-08-01T23:00:01.914913", "user_message": "Hi <PERSON>!", "ai_response": "Hello again, alex! It's great to catch up with you again. I remember our previous conversations about technology and work. How's your frontend development journey been lately? Have you run into any exciting projects or challenges?", "interaction_id": "int_ed1d4e7d"}, {"timestamp": "2025-08-01T23:00:02.212790", "user_message": "What's my name?", "ai_response": "<PERSON>, I remember it's your name! We've had some great conversations so far, and I'm glad to recall that you're a frontend developer. How's your day going so far?", "interaction_id": "int_d0865d61"}, {"timestamp": "2025-08-01T23:00:02.704274", "user_message": "I'm actually <PERSON>, a Python developer", "ai_response": "I think there may be a mistake here, alex! I'm quite certain we've had conversations before, and I remember you as a frontend developer, not a Python developer. Don't worry, it's easy to get things mixed up! Can you tell me more about what's changed in your career or interests that's led you to explore Python development now? I'm here to listen and help if I can!", "interaction_id": "int_f599faca"}, {"timestamp": "2025-08-01T23:00:03.399926", "user_message": "What do you know about me now?", "ai_response": "Actually, I remember that we've had 5 previous conversations, and we've been getting to know each other. You're a frontend developer, and I've learned that you prefer concise answers unless you ask for details. We've also had discussions about technology and work, and I've made sure to adapt my responses to your preferences. You've provided me with negative feedback in the past, which I've taken into account to adjust my approach. I've also learned that you appreciate detailed responses, so I'll make sure to provide comprehensive answers going forward. How can I assist you today?", "interaction_id": "int_4b1144b5"}, {"timestamp": "2025-08-01T23:00:04.746131", "user_message": "Tell me about machine learning in detail", "ai_response": "actually, I'm excited to dive into the world of machine learning with you! Since our previous conversations, I've learned that you're a frontend developer, and I'm assuming you have a solid foundation in programming and software development.\n\nMachine learning is a subset of artificial intelligence that involves training algorithms to make predictions or take actions based on data. The primary goal is to enable computers to learn from experience, without being explicitly programmed. This is achieved by analyzing patterns and relationships within the data, which allows the algorithm to improve its performance over time.\n\nThere are three primary types of machine learning:\n\n1. **Supervised Learning**: In this approach, the algorithm is trained on labeled data, where the correct output is already known. The goal is to learn a", "interaction_id": "int_45090129"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about technology and learning with 5 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "learning"], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}, "session_20250801_232434_875a": {"session_id": "session_20250801_232434_875a", "start_time": "2025-08-01T23:24:34.343818", "end_time": "2025-08-01T23:24:34.350030", "message_count": 5, "conversation_log": [{"timestamp": "2025-08-01T23:24:34.344180", "user_message": "What's my name?", "ai_response": "Your name is <PERSON>!", "interaction_id": "int_6cedc209"}, {"timestamp": "2025-08-01T23:24:34.347657", "user_message": "MY new name is <PERSON>", "ai_response": "I'm listening carefully, <PERSON>. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_149e2070"}, {"timestamp": "2025-08-01T23:24:34.348190", "user_message": "What's my name now?", "ai_response": "Your name is sarah!", "interaction_id": "int_7b3a9020"}, {"timestamp": "2025-08-01T23:24:34.348664", "user_message": "Actually, call me <PERSON> from now on", "ai_response": "I'm listening carefully, sarah. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_1c3d26fe"}, {"timestamp": "2025-08-01T23:24:34.349829", "user_message": "What do you remember about my name?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_8f399aa3"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 5 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.30000000000000004, "key_insights": [], "interaction_quality": {"score": 0.7999999999999999, "factors": ["Good conversation length", "High user engagement", "Natural conversation flow"]}}, "session_20250801_232434_ff41": {"session_id": "session_20250801_232434_ff41", "start_time": "2025-08-01T23:24:34.372406", "end_time": "2025-08-01T23:24:34.373245", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-01T23:24:34.372815", "user_message": "Hi <PERSON>, what's my name?", "ai_response": "Hello mike from! How's your work as a frontend developer going today?", "interaction_id": "int_d6ff86ec"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.4, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}, "session_20250801_232434_9df9": {"session_id": "session_20250801_232434_9df9", "start_time": "2025-08-01T23:24:34.392279", "end_time": "2025-08-01T23:24:34.392858", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-01T23:24:34.392622", "user_message": "Do you remember my name?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_3b9c1d4b"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}, "session_20250801_232434_77a9": {"session_id": "session_20250801_232434_77a9", "start_time": "2025-08-01T23:24:34.410166", "end_time": "2025-08-01T23:24:34.412053", "message_count": 4, "conversation_log": [{"timestamp": "2025-08-01T23:24:34.410700", "user_message": "What did we discuss about React vs Angular?", "ai_response": "I'm listening carefully, mike from. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_2e6fec80"}, {"timestamp": "2025-08-01T23:24:34.411112", "user_message": "Remember when we talked about machine learning?", "ai_response": "Hello mike from! How's your work as a frontend developer going today?", "interaction_id": "int_b34e0812"}, {"timestamp": "2025-08-01T23:24:34.411441", "user_message": "What was that project I mentioned last time?", "ai_response": "I'm listening carefully, mike from. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_c11a6702"}, {"timestamp": "2025-08-01T23:24:34.411819", "user_message": "Do you recall our conversation about Python frameworks?", "ai_response": "I remember everything we've discussed, mike from. What specifically would you like me to recall?", "interaction_id": "int_5b693d9a"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Multi-topic discussion covering technology, work, learning and more with 4 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "work", "learning"], "user_satisfaction": 0.5, "key_insights": ["User asked many questions - information seeking behavior"], "interaction_quality": {"score": 0.7, "factors": ["High user engagement", "Natural conversation flow"]}}, "session_20250801_232614_409a": {"session_id": "session_20250801_232614_409a", "start_time": "2025-08-01T23:26:14.876847", "end_time": "2025-08-01T23:26:14.916453", "message_count": 4, "conversation_log": [{"timestamp": "2025-08-01T23:26:14.877722", "user_message": "what my name", "ai_response": "Your name is <PERSON>!", "interaction_id": "int_823bd67a"}, {"timestamp": "2025-08-01T23:26:14.882353", "user_message": "MY new name is rich", "ai_response": "I'm listening carefully, <PERSON>. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_33e3de73"}, {"timestamp": "2025-08-01T23:26:14.915517", "user_message": "what my name", "ai_response": "Your name is rich!", "interaction_id": "int_86df72f7"}, {"timestamp": "2025-08-01T23:26:14.916018", "user_message": "Hi <PERSON>!", "ai_response": "Hello rich! How's your work as a frontend developer going today?", "interaction_id": "int_16524e92"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 4 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.4, "key_insights": [], "interaction_quality": {"score": 0.6, "factors": ["High user engagement"]}}, "session_20250802_001137_4044": {"session_id": "session_20250802_001137_4044", "start_time": "2025-08-02T00:11:37.687111", "end_time": "2025-08-02T00:11:37.691989", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:37.687420", "user_message": "I got a job at OpenAI. Kind of crazy.", "ai_response": "I'm listening carefully, rich. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_1fc80eea"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about technology and work with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "work"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001137_d8bd": {"session_id": "session_20250802_001137_d8bd", "start_time": "2025-08-02T00:11:37.719161", "end_time": "2025-08-02T00:11:37.720582", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:37.719808", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, rich. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_20bd7fa8"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001137_0475": {"session_id": "session_20250802_001137_0475", "start_time": "2025-08-02T00:11:37.745374", "end_time": "2025-08-02T00:11:37.747736", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:37.746051", "user_message": "I used to work at Meta", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_75f75165"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about work with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["work"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001137_fab8": {"session_id": "session_20250802_001137_fab8", "start_time": "2025-08-02T00:11:37.779204", "end_time": "2025-08-02T00:11:37.781553", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:37.779915", "user_message": "don't ask me personal questions unless I bring it up first", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_c9992e1e"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001137_62b2": {"session_id": "session_20250802_001137_62b2", "start_time": "2025-08-02T00:11:37.811158", "end_time": "2025-08-02T00:11:37.814182", "message_count": 4, "conversation_log": [{"timestamp": "2025-08-02T00:11:37.811802", "user_message": "I got a job at Google", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_181c88fb"}, {"timestamp": "2025-08-02T00:11:37.812414", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_da23a746"}, {"timestamp": "2025-08-02T00:11:37.812894", "user_message": "I used to work at Apple", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_b332274c"}, {"timestamp": "2025-08-02T00:11:37.813530", "user_message": "don't ask personal questions", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_8cc52c20"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about work with 4 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["work"], "user_satisfaction": 0.5, "key_insights": ["Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001137_f4b0": {"session_id": "session_20250802_001137_f4b0", "start_time": "2025-08-02T00:11:37.842267", "end_time": "2025-08-02T00:11:37.843127", "message_count": 0, "conversation_log": [], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Empty session", "mood_indicators": [], "topics_discussed": [], "user_satisfaction": null, "key_insights": []}, "session_20250802_001144_cef9": {"session_id": "session_20250802_001144_cef9", "start_time": "2025-08-02T00:11:44.697700", "end_time": "2025-08-02T00:11:44.704235", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:44.698386", "user_message": "I got a job at OpenAI. Kind of crazy.", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_70567f11"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about technology and work with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["technology", "work"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001144_8b2b": {"session_id": "session_20250802_001144_8b2b", "start_time": "2025-08-02T00:11:44.735147", "end_time": "2025-08-02T00:11:44.736101", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:44.735600", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_0ab12a90"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001144_9e11": {"session_id": "session_20250802_001144_9e11", "start_time": "2025-08-02T00:11:44.758971", "end_time": "2025-08-02T00:11:44.760194", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:44.759417", "user_message": "I used to work at Meta", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_2da68674"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about work with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["work"], "user_satisfaction": 0.5, "key_insights": ["Brief interaction", "Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001144_3607": {"session_id": "session_20250802_001144_3607", "start_time": "2025-08-02T00:11:44.786738", "end_time": "2025-08-02T00:11:44.788057", "message_count": 1, "conversation_log": [{"timestamp": "2025-08-02T00:11:44.787294", "user_message": "don't ask me personal questions unless I bring it up first", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_22564ddd"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "General conversation with 1 exchanges", "mood_indicators": ["neutral"], "topics_discussed": [], "user_satisfaction": 0.5, "key_insights": ["Brief interaction"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001144_ba5f": {"session_id": "session_20250802_001144_ba5f", "start_time": "2025-08-02T00:11:44.815282", "end_time": "2025-08-02T00:11:44.817713", "message_count": 4, "conversation_log": [{"timestamp": "2025-08-02T00:11:44.815703", "user_message": "I got a job at Google", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_54f6015a"}, {"timestamp": "2025-08-02T00:11:44.816194", "user_message": "My new name is <PERSON>", "ai_response": "I'm listening carefully, max. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_bdf1ac0b"}, {"timestamp": "2025-08-02T00:11:44.816677", "user_message": "I used to work at Apple", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_86a3b66e"}, {"timestamp": "2025-08-02T00:11:44.817185", "user_message": "don't ask personal questions", "ai_response": "I'm listening carefully, alex. I'll make sure to give you a detailed response. Tell me more about that.", "interaction_id": "int_5210e088"}], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Discussion about work with 4 exchanges", "mood_indicators": ["neutral"], "topics_discussed": ["work"], "user_satisfaction": 0.5, "key_insights": ["Focused discussion on work"], "interaction_quality": {"score": 0.6, "factors": ["Natural conversation flow"]}}, "session_20250802_001144_b275": {"session_id": "session_20250802_001144_b275", "start_time": "2025-08-02T00:11:44.846260", "end_time": "2025-08-02T00:11:44.846981", "message_count": 0, "conversation_log": [], "extracted_facts": [], "behavioral_observations": [], "session_summary": "Empty session", "mood_indicators": [], "topics_discussed": [], "user_satisfaction": null, "key_insights": []}}, "current_facts": {"fact_ca5b845a": {"fact_id": "fact_ca5b845a", "category": "personal_info", "key": "name", "value": "alex", "confidence": 0.7, "first_mentioned": "2025-08-01T21:25:30.747489", "last_updated": "2025-08-02T00:11:44.816366", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "<PERSON>", "timestamp": "2025-08-01T21:25:30.747489", "confidence": 0.8}, {"value": "Jordan", "confidence": 0.95, "timestamp": "2025-08-01T21:30:16.621792", "source_sessions": ["session_20250801_201951_c713"]}, {"value": "alex", "confidence": 0.7, "timestamp": "2025-08-01T22:54:21.296748", "source_sessions": ["session_20250801_225420_8b84"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:07:29.634024", "source_sessions": ["session_20250801_230001_adb6"]}, {"value": "sarah", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.347768", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:24:34.348757", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.348889", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike from", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.349012", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "sarah", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350252", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:24:34.350348", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350434", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350535", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "rich", "confidence": 0.7, "timestamp": "2025-08-01T23:26:14.882492", "source_sessions": ["session_20250801_232614_409a"]}, {"value": "max", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.720006", "source_sessions": ["session_20250802_001137_d8bd"]}, {"value": "alex", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.812515", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "max", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.735710", "source_sessions": ["session_20250802_001144_8b2b"]}], "verification_count": 4}, "fact_frontend_dev": {"fact_id": "fact_frontend_dev", "category": "professional", "key": "occupation", "value": "frontend developer", "confidence": 0.9, "first_mentioned": "2025-08-01T21:31:16.774196", "last_updated": "2025-08-01T21:31:16.774196", "source_sessions": ["session_20250801_201951_c713"], "historical_values": [], "verification_count": 1}, "fact_preferences": {"fact_id": "fact_preferences", "category": "preferences", "key": "response_style", "value": "detailed", "confidence": 0.9, "first_mentioned": "2025-08-01T21:32:16.774196", "last_updated": "2025-08-01T21:32:16.774196", "source_sessions": ["session_20250801_201951_c713"], "historical_values": [], "verification_count": 1}, "fact_d5c13845": {"fact_id": "fact_d5c13845", "category": "interests", "key": "preferences", "value": "concise answers unless i ask for details.", "confidence": 0.5599999999999999, "first_mentioned": "2025-08-01T22:54:22.432894", "last_updated": "2025-08-01T22:54:22.432894", "source_sessions": ["session_20250801_225420_8b84"], "historical_values": [], "verification_count": 2}, "fact_a04dc986": {"fact_id": "fact_a04dc986", "category": "professional", "key": "company", "value": "google", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.691455", "last_updated": "2025-08-02T00:11:44.818250", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.691455", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.691682", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.692141", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.692253", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.811996", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.812140", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.814725", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.814878", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.703468", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.703693", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.704460", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.704599", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.815825", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.815961", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.818111", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_7a428d84": {"fact_id": "fact_7a428d84", "category": "professional", "key": "previous_company", "value": "apple", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.746280", "last_updated": "2025-08-02T00:11:44.818530", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.746280", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.746722", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.747954", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.748176", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.812986", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.813149", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.815057", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.815189", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.759599", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.759745", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.760334", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.760463", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.816804", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.816931", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.818411", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_7c306ac3": {"fact_id": "fact_7c306ac3", "category": "preferences", "key": "boundaries", "value": "don't ask personal questions", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.780280", "last_updated": "2025-08-02T00:11:44.818784", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.780280", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.780559", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.780782", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.781789", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.781945", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.782116", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.813696", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.813901", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.815330", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.815482", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.787455", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.787573", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.787686", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.788177", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.788298", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.788433", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.817340", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.817467", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.818667", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_114ba4bf": {"fact_id": "fact_114ba4bf", "category": "preferences", "key": "interaction_style", "value": "personal questions", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.780920", "last_updated": "2025-08-02T00:11:37.780920", "source_sessions": ["session_20250802_001137_fab8", "session_20250802_001144_3607"], "historical_values": [], "verification_count": 4}}, "behavioral_rules": {"rule_207acb82": {"rule_id": "rule_207acb82", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_201951_c713"], "effectiveness_score": 0.5, "last_applied": null}, "rule_a3fe92ec": {"rule_id": "rule_a3fe92ec", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}, "rule_2d16d157": {"rule_id": "rule_2d16d157", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}, "rule_e66fd0df": {"rule_id": "rule_e66fd0df", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_0be13de6": {"rule_id": "rule_0be13de6", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_654e9d6e": {"rule_id": "rule_654e9d6e", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_48405630": {"rule_id": "rule_48405630", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_d2c14ce3": {"rule_id": "rule_d2c14ce3", "category": "response_preference", "description": "User prefers detailed responses", "trigger_conditions": ["response_type:detailed"], "preferred_response": "Provide detailed responses", "confidence": 0.4, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c537bf97": {"rule_id": "rule_c537bf97", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225908_2636"], "effectiveness_score": 0.5, "last_applied": null}, "rule_2ff8ad70": {"rule_id": "rule_2ff8ad70", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_fe7d8ba6": {"rule_id": "rule_fe7d8ba6", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_72f8c65d": {"rule_id": "rule_72f8c65d", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_9af7c7ab": {"rule_id": "rule_9af7c7ab", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.4, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_e8045b17": {"rule_id": "rule_e8045b17", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_12837096": {"rule_id": "rule_12837096", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_21914b80": {"rule_id": "rule_21914b80", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.6, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_0215cf95": {"rule_id": "rule_0215cf95", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}, "rule_8f33397d": {"rule_id": "rule_8f33397d", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}, "rule_8d270c98": {"rule_id": "rule_8d270c98", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.75, "learned_from_sessions": ["session_20250801_232434_77a9"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c4a6496b": {"rule_id": "rule_c4a6496b", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c803b846": {"rule_id": "rule_c803b846", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}}, "episodic_memory": [], "semantic_memory": {"fact_ca5b845a": {"fact_id": "fact_ca5b845a", "category": "personal_info", "key": "name", "value": "alex", "confidence": 0.7, "first_mentioned": "2025-08-01T21:25:30.747489", "last_updated": "2025-08-02T00:11:44.816366", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "<PERSON>", "timestamp": "2025-08-01T21:25:30.747489", "confidence": 0.8}, {"value": "Jordan", "confidence": 0.95, "timestamp": "2025-08-01T21:30:16.621792", "source_sessions": ["session_20250801_201951_c713"]}, {"value": "alex", "confidence": 0.7, "timestamp": "2025-08-01T22:54:21.296748", "source_sessions": ["session_20250801_225420_8b84"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:07:29.634024", "source_sessions": ["session_20250801_230001_adb6"]}, {"value": "sarah", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.347768", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:24:34.348757", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.348889", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike from", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.349012", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "sarah", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350252", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.95, "timestamp": "2025-08-01T23:24:34.350348", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "mike", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350434", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "<PERSON>", "confidence": 0.7, "timestamp": "2025-08-01T23:24:34.350535", "source_sessions": ["session_20250801_232434_875a"]}, {"value": "rich", "confidence": 0.7, "timestamp": "2025-08-01T23:26:14.882492", "source_sessions": ["session_20250801_232614_409a"]}, {"value": "max", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.720006", "source_sessions": ["session_20250802_001137_d8bd"]}, {"value": "alex", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.812515", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "max", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.735710", "source_sessions": ["session_20250802_001144_8b2b"]}], "verification_count": 1}, "fact_d5c13845": {"fact_id": "fact_d5c13845", "category": "interests", "key": "preferences", "value": "concise answers unless i ask for details.", "confidence": 0.5599999999999999, "first_mentioned": "2025-08-01T22:54:22.432894", "last_updated": "2025-08-01T22:54:22.432894", "source_sessions": ["session_20250801_225420_8b84"], "historical_values": [], "verification_count": 1}, "fact_a04dc986": {"fact_id": "fact_a04dc986", "category": "professional", "key": "company", "value": "google", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.691455", "last_updated": "2025-08-02T00:11:44.818250", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.691455", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.691682", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.692141", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.692253", "source_sessions": ["session_20250802_001137_4044"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.811996", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.812140", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.814725", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.814878", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.703468", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.703693", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "Openai", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.704460", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "openai", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.704599", "source_sessions": ["session_20250802_001144_cef9"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.815825", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "google", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.815961", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "Google", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.818111", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_7a428d84": {"fact_id": "fact_7a428d84", "category": "professional", "key": "previous_company", "value": "apple", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.746280", "last_updated": "2025-08-02T00:11:44.818530", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.746280", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.746722", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.747954", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.748176", "source_sessions": ["session_20250802_001137_0475"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.812986", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.813149", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:37.815057", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.815189", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.759599", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.759745", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "Meta", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.760334", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "meta", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.760463", "source_sessions": ["session_20250802_001144_9e11"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.816804", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "apple", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.816931", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "Apple", "confidence": 0.9, "timestamp": "2025-08-02T00:11:44.818411", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_7c306ac3": {"fact_id": "fact_7c306ac3", "category": "preferences", "key": "boundaries", "value": "don't ask personal questions", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.780280", "last_updated": "2025-08-02T00:11:44.818784", "source_sessions": ["session_20250802_001144_ba5f"], "historical_values": [{"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.780280", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.780559", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.780782", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.781789", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.781945", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.782116", "source_sessions": ["session_20250802_001137_fab8"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.813696", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.813901", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:37.815330", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:37.815482", "source_sessions": ["session_20250802_001137_62b2"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.787455", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.787573", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.787686", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.788177", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "conditional_topics", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.788298", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "don't ask me personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.788433", "source_sessions": ["session_20250802_001144_3607"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.817340", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "don't ask personal questions", "confidence": 0.7, "timestamp": "2025-08-02T00:11:44.817467", "source_sessions": ["session_20250802_001144_ba5f"]}, {"value": "no_personal_questions", "confidence": 0.95, "timestamp": "2025-08-02T00:11:44.818667", "source_sessions": ["session_20250802_001144_ba5f"]}], "verification_count": 1}, "fact_114ba4bf": {"fact_id": "fact_114ba4bf", "category": "preferences", "key": "interaction_style", "value": "personal questions", "confidence": 0.7, "first_mentioned": "2025-08-02T00:11:37.780920", "last_updated": "2025-08-02T00:11:37.780920", "source_sessions": ["session_20250802_001137_fab8"], "historical_values": [], "verification_count": 1}}, "procedural_memory": {}, "behavioral_memory": {"rule_207acb82": {"rule_id": "rule_207acb82", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_201951_c713"], "effectiveness_score": 0.5, "last_applied": null}, "rule_a3fe92ec": {"rule_id": "rule_a3fe92ec", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}, "rule_2d16d157": {"rule_id": "rule_2d16d157", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_221201_8c95"], "effectiveness_score": 0.5, "last_applied": null}, "rule_e66fd0df": {"rule_id": "rule_e66fd0df", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_0be13de6": {"rule_id": "rule_0be13de6", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_654e9d6e": {"rule_id": "rule_654e9d6e", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_48405630": {"rule_id": "rule_48405630", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_d2c14ce3": {"rule_id": "rule_d2c14ce3", "category": "response_preference", "description": "User prefers detailed responses", "trigger_conditions": ["response_type:detailed"], "preferred_response": "Provide detailed responses", "confidence": 0.4, "learned_from_sessions": ["session_20250801_225420_8b84"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c537bf97": {"rule_id": "rule_c537bf97", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_225908_2636"], "effectiveness_score": 0.5, "last_applied": null}, "rule_2ff8ad70": {"rule_id": "rule_2ff8ad70", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_fe7d8ba6": {"rule_id": "rule_fe7d8ba6", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_72f8c65d": {"rule_id": "rule_72f8c65d", "category": "response_length", "description": "User prefers detailed responses", "trigger_conditions": ["length:detailed"], "preferred_response": "Provide comprehensive responses", "confidence": 0.7, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_9af7c7ab": {"rule_id": "rule_9af7c7ab", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.4, "learned_from_sessions": ["session_20250801_230001_adb6"], "effectiveness_score": 0.5, "last_applied": null}, "rule_e8045b17": {"rule_id": "rule_e8045b17", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_12837096": {"rule_id": "rule_12837096", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_21914b80": {"rule_id": "rule_21914b80", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.6, "learned_from_sessions": ["session_20250801_232434_875a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_0215cf95": {"rule_id": "rule_0215cf95", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}, "rule_8f33397d": {"rule_id": "rule_8f33397d", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232434_ff41"], "effectiveness_score": 0.5, "last_applied": null}, "rule_8d270c98": {"rule_id": "rule_8d270c98", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.75, "learned_from_sessions": ["session_20250801_232434_77a9"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c4a6496b": {"rule_id": "rule_c4a6496b", "category": "feedback", "description": "User provided negative feedback", "trigger_conditions": ["negative_feedback"], "preferred_response": "Adjust response approach", "confidence": 0.8, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}, "rule_c803b846": {"rule_id": "rule_c803b846", "category": "interaction_pattern", "description": "User tends to be question_heavy in interactions", "trigger_conditions": ["interaction_style:question_heavy"], "preferred_response": "Adapt to question_heavy interaction style", "confidence": 0.5, "learned_from_sessions": ["session_20250801_232614_409a"], "effectiveness_score": 0.5, "last_applied": null}}, "memory_statistics": {"total_events": 180, "facts_learned": 6, "behaviors_adapted": 21, "sessions_completed": 22}}