# Nova Memory AI System

A dedicated AI Memory Agent that works alongside <PERSON> to provide persistent memory capabilities.

## Core Concept

The Nova Memory AI is a companion system that:
- **Only stores & retrieves memory** - Pure data storage and recall
- **Activates when <PERSON> forgets** - Seamless background operation  
- **Feeds missing info back automatically** - Transparent to the user

## System Architecture

```
User ↔ Nova (Main AI) ↔ Memory AI ↔ JSON Storage
```

### Components

1. **Nova (Main AI)**: <PERSON>les conversation, personality, voice (Cartesia)
2. **Memory AI Agent**: Dedicated memory storage and retrieval
3. **Memory Storage**: JSON-based data persistence
4. **Interface Layer**: Communication between Nova and Memory AI

## How It Works

### Information Flow
1. User talks to <PERSON> normally
2. <PERSON> sends conversation copy to Memory AI
3. Memory AI extracts and stores facts
4. When <PERSON> forgets, it queries Memory AI
5. Memory AI returns the needed information instantly

### Example Interaction
```
User: "My name is <PERSON><PERSON>. I like flowers."
Nova: "Nice to meet you, <PERSON><PERSON>! Flowers are beautiful."
Memory AI: → stores {name: "<PERSON><PERSON>", interests: ["flowers"]}

Later (next day):
User: "Do you remember my name?"
<PERSON> (forgets): → queries Memory AI → gets "Chiara"
Nova: "Of course, you're <PERSON><PERSON>!"
```

## Memory AI Responsibilities

- **Fact Extraction**: Names, interests, relationships, places, events
- **Conversation Logging**: Daily grouped conversation history
- **Context Retrieval**: On-demand information lookup
- **Memory Optimization**: Compression and consolidation of old data

## Benefits

- **Modular**: Memory separate from Nova's core functions
- **Scalable**: Easy upgrade path to vector/graph databases
- **Reliable**: Persistent memory across Nova sessions
- **Human-like**: Natural forgetting with reliable recall

## Project Structure

```
nova-memory-ai/
├── src/
│   ├── memory_ai/          # Core Memory AI agent
│   ├── storage/            # JSON storage system
│   ├── interface/          # Nova-Memory AI communication
│   └── utils/              # Helper functions
├── data/
│   ├── facts/              # Extracted facts storage
│   ├── conversations/      # Conversation logs
│   └── config/             # System configuration
├── tests/                  # Test suite
└── examples/               # Demo scenarios
```

## Getting Started

1. Install dependencies
2. Configure storage paths
3. Initialize Memory AI agent
4. Connect to Nova interface
5. Start memory collection

## Future Enhancements

- Vector similarity search for better fact retrieval
- Graph database for relationship mapping
- Advanced conversation compression
- Multi-user memory isolation
- Real-time memory synchronization
