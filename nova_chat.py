#!/usr/bin/env python3
"""
Nova AI Chat - Advanced AI with Perfect Memory System
Integrated with EventDrivenMemorySystem for professional-grade memory
"""

import os
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")

# Try to import GROQ
try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    print("⚠️  GROQ not installed. Install with: pip install groq")

# Import the Mem0-style memory system
try:
    from mem0_memory_system import AdvancedMemoryAgent
    MEM0_MEMORY_AVAILABLE = True
    print("✅ Mem0-style memory system loaded")
except ImportError as e:
    MEM0_MEMORY_AVAILABLE = False
    print(f"❌ Mem0-style memory system not found: {e}")
    print("⚠️  Falling back to basic memory system")

class NovaAI:
    """Nova AI with Mem0-Style Memory System"""

    def __init__(self):
        print("🌟 Starting Nova AI with Mem0-Style Memory...")

        # Initialize Mem0-style memory system
        if MEM0_MEMORY_AVAILABLE:
            try:
                self.memory = AdvancedMemoryAgent("nova_memory.json")
                print("✅ Mem0-style memory system initialized")

                # Load and display memory stats
                self._display_memory_status()

            except Exception as e:
                print(f"❌ Error initializing Mem0 memory: {e}")
                self.memory = self._create_basic_memory()
                print("⚠️  Using basic memory system")
        else:
            # Fallback to basic memory
            self.memory = self._create_basic_memory()
            print("⚠️  Using basic memory system")
        
        # Setup AI
        self.groq_client = None
        self.ai_available = False
        self.setup_ai()
        
        # Show personalized greeting
        self._show_personalized_greeting()

        print("💬 Start chatting! (type 'quit' to exit)")
        print("💡 Commands: 'memory', 'json', 'stats', 'facts', 'history', 'timeline', 'insights', 'emotions', 'patterns', 'health', 'sessions'")
        print("-" * 50)
    
    def _create_basic_memory(self):
        """Create basic memory fallback"""
        class BasicMemory:
            def __init__(self):
                self.facts = {}
                self.conversations = []
            
            def process_conversation(self, user_msg, ai_msg):
                self.conversations.append({
                    'timestamp': datetime.now().isoformat(),
                    'user': user_msg,
                    'ai': ai_msg
                })
                return {'memory_operations': 0, 'operations': []}
            
            def query_memory(self, query):
                return {'found': False, 'response': "Basic memory active"}
            
            def get_user_profile(self):
                return self.facts
        
        return BasicMemory()
    
    def setup_ai(self):
        """Setup GROQ AI"""
        try:
            if GROQ_AVAILABLE:
                # Try environment variable first, then fallback to hardcoded key
                api_key = os.getenv('GROQ_API_KEY')
                if not api_key:
                    # Hardcoded API key (replace with your actual key)
                    api_key = "********************************************************"
                
                if api_key and len(api_key) > 20:
                    self.groq_client = Groq(api_key=api_key)
                    self.ai_available = True
                    print("✅ GROQ AI connected")
                else:
                    print("❌ GROQ_API_KEY not found - please add your API key")
            else:
                print("❌ GROQ not available")
        except Exception as e:
            print(f"❌ AI setup failed: {e}")
    
    def get_user_profile(self):
        """Get user profile from advanced memory system with verification"""
        if hasattr(self.memory, 'get_verified_user_profile'):
            return self.memory.get_verified_user_profile()
        elif hasattr(self.memory, 'get_user_profile'):
            return self.memory.get_user_profile()
        return {}
    
    def get_context(self, user_message):
        """Get context from Mem0-style memory with conversation state awareness"""
        if hasattr(self.memory, 'get_memory_context'):
            context = self.memory.get_memory_context(user_message)

            # Build context string for AI
            context_parts = []

            # Add conversation state instructions to prevent inappropriate greetings
            if hasattr(self.memory, 'get_ai_context_instructions'):
                instructions = self.memory.get_ai_context_instructions()
                if instructions:
                    context_parts.append(f"CONVERSATION CONTEXT: {instructions}")

            # Current facts
            if context.get('current_facts'):
                facts_info = [f"{k}: {v}" for k, v in context['current_facts'].items()]
                context_parts.append("CURRENT FACTS: " + ", ".join(facts_info))

            # User info
            if context.get('user_info'):
                user_info = context['user_info']
                if user_info.get('name'):
                    context_parts.append(f"USER NAME: {user_info['name']}")

            return "\n".join(context_parts) if context_parts else "No memory context available"

        return "No memory context available"
    
    def generate_ai_response(self, user_message, context):
        """Generate completely dynamic AI response based on actual memory data"""
        if not self.ai_available:
            return None

        # Extract dynamic memory context
        memory_context = self._extract_dynamic_memory_context()

        # Create dynamic system prompt based on actual memory data
        system_prompt = self._create_dynamic_system_prompt(user_message, context, memory_context)

        try:
            response = self.groq_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                model="llama3-8b-8192",
                temperature=0.7,
                max_tokens=50
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"AI error: {e}")
            return None

    def _create_dynamic_system_prompt(self, user_message, context, memory_context):
        """Create completely dynamic system prompt based on actual memory data"""
        # Get current name from facts, not user_info
        current_facts = memory_context.get('current_facts', {})
        user_name = current_facts.get('name', 'the user')

        user_info = memory_context.get('user_info', {})
        facts = memory_context.get('facts', {})
        recent_topics = memory_context.get('recent_topics', [])
        relationship_context = memory_context.get('relationship_context', {})
        session_info = memory_context.get('session_info', {})

        # Build dynamic behavioral instructions based on actual memory state
        behavioral_instructions = []

        # Dynamic conversation state rules based on actual relationship data
        if relationship_context.get('is_established_user', False):
            behavioral_instructions.extend([
                f"You are continuing an ongoing relationship with {user_name}.",
                "Reference your shared history naturally when relevant.",
                "Avoid re-introductions or basic getting-to-know-you questions."
            ])
        else:
            behavioral_instructions.extend([
                f"You are building a new relationship with {user_name}.",
                "Be welcoming and help establish rapport naturally."
            ])

        # Add context from actual recent conversations
        if recent_topics:
            recent_context = ", ".join([f'"{topic[:30]}..."' if len(topic) > 30 else f'"{topic}"'
                                      for topic in recent_topics[-3:]])
            behavioral_instructions.append(f"Recent conversation topics include: {recent_context}")

        # Add context from actual stored facts
        fact_context = []
        if facts.get('occupation'):
            fact_context.append(f"{user_name} works as {facts['occupation']}")
        if facts.get('interests'):
            fact_context.append(f"{user_name} is interested in {facts['interests']}")
        if facts.get('name') and facts['name'] != user_name:
            fact_context.append(f"User's name is {facts['name']}")

        if fact_context:
            behavioral_instructions.append("Known facts: " + "; ".join(fact_context))

        # Dynamic session context
        if session_info.get('total_sessions', 0) > 1:
            behavioral_instructions.append(f"This is session #{session_info['total_sessions']} with {user_name}")

        # Create the dynamic prompt
        prompt_parts = [
            f"You are Nova, a memory-enabled AI companion talking with {user_name}.",
            "",
            "CURRENT MEMORY CONTEXT:",
            context,
            "",
            "DYNAMIC BEHAVIORAL CONTEXT:",
        ]

        for instruction in behavioral_instructions:
            prompt_parts.append(f"- {instruction}")

        prompt_parts.extend([
            "",
            "RESPONSE GUIDELINES:",
            "- Be natural, warm, and conversational (1-2 sentences max)",
            "- Use the memory context to inform your response authentically",
            "- Reference shared history when relevant",
            "- Never make up information not in your memory",
            "- Respond as a friend who remembers your conversations",
            "",
            f"Respond naturally to {user_name} based on your actual shared history:"
        ])

        return "\n".join(prompt_parts)
    
    def generate_fallback_response(self, user_message):
        """Generate dynamic response based on actual memory data when AI is not available"""
        return self._generate_memory_driven_response(user_message)

    def _generate_memory_driven_response(self, user_message):
        """Generate completely dynamic responses based on actual memory data"""
        try:
            # Get all memory context dynamically
            memory_context = self._extract_dynamic_memory_context()

            # Generate response based on actual stored data
            return self._create_contextual_response(user_message, memory_context)

        except Exception as e:
            print(f"❌ Memory-driven response error: {e}")
            return "I'm having trouble accessing my memory right now, but I'm here to chat!"

    def _extract_dynamic_memory_context(self):
        """Extract all relevant context from actual memory data"""
        try:
            # Use the enhanced dynamic context from memory system
            if hasattr(self.memory, 'get_dynamic_conversation_context'):
                return self.memory.get_dynamic_conversation_context()

            # Fallback to basic context extraction
            context = {
                'user_info': {},
                'conversation_history': [],
                'recent_topics': [],
                'relationship_context': {},
                'session_info': {},
                'facts': {}
            }

            # Get user profile from memory
            if hasattr(self.memory, 'get_user_profile'):
                profile = self.memory.get_user_profile()
                context['user_info'] = profile.get('user_info', {})
                context['facts'] = profile.get('facts', {})

            # Get conversation history
            if hasattr(self.memory, 'get_conversation_history'):
                context['conversation_history'] = self.memory.get_conversation_history()

                # Extract recent topics from actual conversations
                user_messages = [msg for msg in context['conversation_history']
                               if msg.get('role') == 'user'][-5:]  # Last 5 user messages
                context['recent_topics'] = [msg.get('content', '') for msg in user_messages]

            # Get session information
            if hasattr(self.memory, 'get_session_info'):
                context['session_info'] = self.memory.get_session_info()

            # Get relationship context
            if hasattr(self.memory, 'get_conversation_state'):
                context['relationship_context'] = self.memory.get_conversation_state()

            return context

        except Exception as e:
            print(f"❌ Context extraction error: {e}")
            return {}

    def _create_contextual_response(self, user_message, memory_context):
        """Create response based on actual memory context"""
        # Get current name from facts, not user_info
        current_facts = memory_context.get('current_facts', {})
        user_name = current_facts.get('name', 'there')
        message_lower = user_message.lower()

        # Use actual conversation history to inform response
        recent_topics = memory_context.get('recent_topics', [])
        conversation_history = memory_context.get('conversation_history', [])
        facts = memory_context.get('facts', {})

        # Generate response based on actual stored context
        if any(greeting in message_lower for greeting in ['hi', 'hello', 'hey', 'nova']):
            return self._generate_greeting_from_memory(user_name, memory_context)

        # Reference actual conversation history
        if recent_topics:
            return self._generate_contextual_continuation(user_message, recent_topics, user_name)

        # Use actual facts to inform response
        if facts:
            return self._generate_fact_based_response(user_message, facts, user_name)

        # Default response using actual user name from memory
        return f"I'm listening, {user_name}. What's on your mind?"

    def _generate_greeting_from_memory(self, user_name, memory_context):
        """Generate greeting based on actual memory data"""
        session_info = memory_context.get('session_info', {})
        relationship_context = memory_context.get('relationship_context', {})

        # Use actual session data to inform greeting
        if session_info.get('is_first_time', True):
            return f"Hi there! I'm Nova. What's your name?"

        # Use actual relationship data
        if relationship_context.get('is_established_user', False):
            last_topics = session_info.get('last_topics', [])
            if last_topics:
                topic_ref = last_topics[0] if len(last_topics) == 1 else f"{last_topics[0]} and other topics"
                return f"Hey {user_name}! Continuing from where we left off with {topic_ref}."
            else:
                return f"Hey {user_name}! Good to continue our conversation."

        return f"Hi {user_name}! What's up?"

    def _generate_contextual_continuation(self, user_message, recent_topics, user_name):
        """Generate response that references actual recent conversation topics"""
        if not recent_topics:
            return f"Tell me more, {user_name}."

        # Reference the most recent actual topic
        last_topic = recent_topics[-1]
        if len(last_topic) > 30:
            last_topic = last_topic[:30] + "..."

        # Create natural continuation based on actual conversation
        return f"Building on what you mentioned about '{last_topic}', tell me more about that."

    def _generate_fact_based_response(self, user_message, facts, user_name):
        """Generate response using actual stored facts"""
        message_lower = user_message.lower()

        # Reference actual stored facts
        if 'work' in message_lower or 'job' in message_lower:
            if 'occupation' in facts:
                return f"How's your work as {facts['occupation']} going, {user_name}?"

        if 'interest' in message_lower or 'hobby' in message_lower:
            if 'interests' in facts:
                return f"Still enjoying {facts['interests']}, {user_name}?"

        # General response using actual user name
        return f"That's interesting, {user_name}. Tell me more."
    
    def _get_relationship_duration(self):
        """Get how long we've known the user"""
        try:
            if hasattr(self.memory, 'memory_system') and hasattr(self.memory.memory_system, '_calculate_relationship_duration'):
                return self.memory.memory_system._calculate_relationship_duration()
            elif hasattr(self.memory, 'memory_system') and self.memory.memory_system.conversation:
                # Fallback calculation
                if not self.memory.memory_system.conversation:
                    return "just met"
                
                first_conversation = datetime.fromisoformat(self.memory.memory_system.conversation[0]['timestamp'])
                duration = datetime.now() - first_conversation
                
                if duration.days == 0:
                    return "today"
                elif duration.days == 1:
                    return "since yesterday"
                elif duration.days < 7:
                    return f"{duration.days} days"
                else:
                    return f"{duration.days // 7} weeks"
        except Exception as e:
            print(f"Error calculating relationship duration: {e}")
        return "just now"
    
    def handle_command(self, command):
        """Handle Mem0-style memory commands"""
        if command == 'memory' or command == 'stats':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'get_memory_stats'):
                stats = self.memory.get_memory_stats()
                print("\n🧠 Mem0-Style Memory Status:")
                print("-" * 40)
                print(f"👤 User: {stats.get('user_name', 'Unknown')} (ID: {stats.get('user_id', 'N/A')})")
                print(f"� Total Facts: {stats.get('total_facts', 0)}")
                print(f"📝 Memory Events: {stats.get('total_events', 0)}")
                print(f"💬 Messages: {stats.get('total_messages', 0)}")

                facts = stats.get('facts_breakdown', {})
                if facts:
                    print("\n� Current Facts:")
                    for fact_type, value in facts.items():
                        print(f"   {fact_type}: {value}")
            else:
                print("\n🧠 Basic memory system active")
            return True
        
        elif command == 'json':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'memory_system') and hasattr(self.memory.memory_system, 'get_full_memory_json'):
                memory_json = self.memory.memory_system.get_full_memory_json()
                print("\n📦 Complete Memory JSON:")
                print("-" * 40)
                print(json.dumps(memory_json, indent=2, ensure_ascii=False))
            else:
                print("⚠️  JSON export not available")
            return True

        elif command == 'facts':
            if MEM0_MEMORY_AVAILABLE:
                context = self.memory.get_memory_context()
                facts = context.get('current_facts', {})
                if facts:
                    print("\n📋 Current Facts:")
                    print("-" * 40)
                    for fact_type, value in facts.items():
                        print(f"   {fact_type.title()}: {value}")
                else:
                    print("\n📋 No facts stored yet")
            return True

        elif command == 'history':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'recall_history'):
                print("\n🕰️  Historical Memory Recall")
                print("-" * 40)
                print("Ask me about your previous information:")
                print("• 'What was my old name?'")
                print("• 'What did I used to work as?'")
                print("• 'What were my old interests?'")
                print("• 'What have I changed about myself?'")
                print("\nOr type a specific question about your history.")
            return True

        elif command == 'timeline':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'get_timeline'):
                timeline = self.memory.get_timeline()
                if timeline:
                    print("\n📅 Memory Timeline:")
                    print("-" * 40)
                    for entry in timeline:
                        status_icon = "🔄" if entry['status'] == 'current' else "📜"
                        print(f"{status_icon} {entry['formatted_time']}: {entry['fact_type']} = {entry['value']}")
                else:
                    print("\n📅 No timeline data available yet")
            return True

        elif command == 'insights':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'get_semantic_insights'):
                insights = self.memory.get_semantic_insights()
                print("\n🧠 Semantic Insights:")
                print("-" * 40)

                if insights.get('relationship_strength'):
                    print("🔗 Fact Relationships:")
                    for relationship, strength in insights['relationship_strength'].items():
                        print(f"   {relationship}: {strength:.2f}")

                if insights.get('context_analysis'):
                    print("\n📊 Context Distribution:")
                    for context, count in insights['context_analysis'].items():
                        print(f"   {context.title()}: {count} facts")
            return True

        elif command == 'emotions':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'get_emotional_timeline'):
                timeline = self.memory.get_emotional_timeline()
                if timeline:
                    print("\n💭 Emotional Timeline:")
                    print("-" * 40)
                    for event in timeline[-5:]:  # Show last 5 emotional events
                        sentiment_icon = "😊" if event['sentiment'] == 'positive' else "😔" if event['sentiment'] == 'negative' else "😐"
                        emotions = ", ".join(event.get('emotions', []))
                        print(f"{sentiment_icon} {event.get('summary', 'Unknown')} - {emotions}")
                else:
                    print("\n💭 No emotional timeline data available yet")
            return True

        elif command == 'patterns':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'detect_memory_patterns'):
                patterns = self.memory.detect_memory_patterns()
                if patterns:
                    print("\n🔍 Detected Memory Patterns:")
                    print("-" * 40)
                    for pattern in patterns:
                        print(f"📈 {pattern['pattern_type'].title()}: {pattern['confidence']:.1%} confidence")
                        if pattern.get('supporting_evidence'):
                            print(f"   Evidence: {', '.join(pattern['supporting_evidence'])}")
                else:
                    print("\n🔍 No patterns detected yet")
            return True

        elif command == 'health':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'analyze_memory_health'):
                health = self.memory.analyze_memory_health()
                print("\n🏥 Memory System Health:")
                print("-" * 40)
                print(f"Overall Health Score: {health['health_score']:.1%}")
                print(f"Total Facts: {health['total_facts']}")
                print(f"Facts with History: {health['facts_with_history']}")
                print(f"Recent Activity: {health['recent_activity']}")
                print(f"Relationships: {health['relationship_count']}")
                print(f"Emotional Events: {health['emotional_events']}")

                if health.get('recommendations'):
                    print("\n💡 Recommendations:")
                    for rec in health['recommendations']:
                        print(f"   • {rec}")
            return True

        elif command == 'sessions':
            if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'get_session_info'):
                session_info = self.memory.get_session_info()
                print("\n🔄 Conversation Sessions:")
                print("-" * 40)
                print(f"Total Sessions: {session_info['total_sessions']}")

                if session_info['is_first_time']:
                    print("Status: First-time user")
                else:
                    print(f"Status: Returning user")
                    print(f"Last seen: {session_info.get('time_since_last', 'unknown')} ago")

                    if session_info.get('last_topics'):
                        print(f"Last topics: {', '.join(session_info['last_topics'])}")

                    if session_info.get('last_duration'):
                        print(f"Last session duration: {session_info['last_duration']}")
            return True
        

        
        return False
    
    def _display_memory_status(self):
        """Display memory status on startup"""
        if hasattr(self.memory, 'get_memory_stats'):
            stats = self.memory.get_memory_stats()
            total_facts = stats.get('total_facts', 0)
            total_conversations = stats.get('total_conversations', 0)
            user_name = stats.get('user_name')

            if total_facts > 0 or total_conversations > 0:
                print(f"🧠 Loaded memory: {total_conversations} messages, {total_facts} facts")
                if user_name:
                    print(f"   👤 User: {user_name}")
    
    def _show_personalized_greeting(self):
        """Show intelligent session-aware greeting"""
        try:
            # Get conversation context for intelligent greeting
            if hasattr(self.memory, 'get_conversation_context'):
                context = self.memory.get_conversation_context()
                greeting_message = context.get('greeting_message', 'Hi there!')

                # Show appropriate status message
                if self.ai_available:
                    print(f"✅ Nova AI ready! {greeting_message}")
                else:
                    print(f"⚠️  Nova AI ready with smart responses! {greeting_message}")

                # Show additional context for returning users
                if context.get('can_reference_previous', False):
                    last_duration = context.get('last_duration')
                    if last_duration and last_duration != 'unknown':
                        print(f"   💭 Our last conversation lasted {last_duration}")

            else:
                # Fallback to basic greeting - use current facts for accurate name
                if hasattr(self.memory, 'get_dynamic_conversation_context'):
                    context = self.memory.get_dynamic_conversation_context()
                    current_facts = context.get('current_facts', {})
                    user_name = current_facts.get('name', 'there')
                else:
                    user_profile = self.get_user_profile()
                    user_name = user_profile.get('user_info', {}).get('name') or user_profile.get('facts', {}).get('name', 'there')

                if self.ai_available:
                    print(f"✅ Nova AI ready! Hi {user_name}!")
                else:
                    print(f"⚠️  Nova AI ready with smart responses! Hi {user_name}!")

        except Exception as e:
            print(f"✅ Nova AI ready! Let's chat!")
            print(f"Debug: Greeting error - {e}")
    
    def _get_basic_context(self, user_message):
        """Get basic context when advanced memory is not available"""
        if hasattr(self.memory, 'get_user_profile'):
            profile = self.memory.get_user_profile()
            if profile:
                return f"User profile: {profile}"
        return "Basic memory context"
    
    def chat(self, user_message):
        """Enhanced chat with advanced memory integration and historical recall"""
        if not user_message.strip():
            return "Hey! What's up?"

        # Handle commands
        if self.handle_command(user_message.strip()):
            return None

        # Check if this is a historical query first
        if self._is_historical_query(user_message):
            return self._handle_historical_query(user_message)

        # Get comprehensive context
        context = self.get_context(user_message)

        # Generate AI response
        ai_response = self.generate_ai_response(user_message, context)
        if not ai_response:
            ai_response = self.generate_fallback_response(user_message)
        
        # Process through enhanced memory system
        if MEM0_MEMORY_AVAILABLE and hasattr(self.memory, 'process_conversation'):
            try:
                result = self.memory.process_conversation(user_message, ai_response)
                # Memory operations are displayed automatically by the memory system

            except Exception as e:
                print(f"❌ Memory processing error: {e}")

        return ai_response

    def _is_historical_query(self, user_message: str) -> bool:
        """Check if the user is asking about historical information"""
        query_lower = user_message.lower()

        historical_indicators = [
            'old', 'previous', 'used to', 'former', 'before', 'originally',
            'what was', 'what did i', 'what have i changed', 'timeline',
            'history', 'evolution', 'over time', 'changed about',
            'talking about', 'discussed', 'conversation', 'we talked',
            'first', 'earlier', 'last time', 'recent', 'ago'
        ]

        return any(indicator in query_lower for indicator in historical_indicators)

    def _handle_historical_query(self, user_message: str) -> str:
        """Handle historical memory recall queries including conversation history"""
        if not MEM0_MEMORY_AVAILABLE:
            return "I don't have access to historical memory right now."

        try:
            query_lower = user_message.lower()

            # Check if this is asking about conversation history
            if any(phrase in query_lower for phrase in ['talking about', 'discussed', 'conversation', 'we talked', 'first', 'earlier']):
                return self._handle_conversation_history_query(user_message)

            # Handle fact history queries
            if hasattr(self.memory, 'recall_history'):
                recall_result = self.memory.recall_history(user_message)

                if recall_result['found']:
                    results = recall_result['results']

                    if len(results) == 1:
                        result = results[0]
                        if 'previous_value' in result:
                            # This is a change summary
                            return f"You used to {result['fact_type']} as {result['previous_value']}, but now you {result['fact_type']} as {result['current_value']}. You changed this {result['formatted_time']}."
                        else:
                            # This is a historical value
                            return f"Your {result['fact_type']} was {result['value']} ({result['formatted_time']})."

                    elif len(results) > 1:
                        # Multiple results
                        if 'previous_value' in results[0]:
                            # Changes summary
                            changes = []
                            for result in results:
                                changes.append(f"• {result['fact_type'].title()}: {result['previous_value']} → {result['current_value']} ({result['formatted_time']})")
                            return f"Here's what you've changed:\n" + "\n".join(changes)
                        else:
                            # Historical values
                            values = []
                            for result in results:
                                values.append(f"• {result['value']} ({result['formatted_time']})")
                            return f"Your {results[0]['fact_type']} history:\n" + "\n".join(values)

            # If no specific results found, try conversation history as fallback
            return self._handle_conversation_history_query(user_message)

        except Exception as e:
            print(f"❌ Historical recall error: {e}")
            return "Sorry, I had trouble accessing your historical information."

    def _handle_conversation_history_query(self, user_message: str) -> str:
        """Handle queries about conversation history"""
        try:
            # Get conversation history from memory
            if hasattr(self.memory, 'get_conversation_history'):
                conversations = self.memory.get_conversation_history()
            elif hasattr(self.memory.memory_system, 'data'):
                conversations = self.memory.memory_system.data.get('conversation', [])
            else:
                return "I don't have access to conversation history right now."

            if not conversations:
                return "We haven't had any conversations yet."

            query_lower = user_message.lower()

            # Handle specific conversation history queries
            if 'first' in query_lower or 'beginning' in query_lower:
                # Get first few messages
                first_messages = conversations[:4]  # First 2 exchanges
                user_messages = [msg for msg in first_messages if msg.get('role') == 'user']
                if user_messages:
                    first_user_msg = user_messages[0].get('content', '')
                    return f"We first talked about: {first_user_msg}"
                else:
                    return "I can see we've talked before, but I can't find the first message."

            elif 'last' in query_lower or 'recent' in query_lower:
                # Get recent messages
                recent_messages = conversations[-4:]  # Last 2 exchanges
                user_messages = [msg for msg in recent_messages if msg.get('role') == 'user']
                if user_messages:
                    last_user_msg = user_messages[-1].get('content', '')
                    return f"We recently talked about: {last_user_msg}"
                else:
                    return "I can see we've talked recently, but I can't find the specific message."

            else:
                # General conversation summary
                user_messages = [msg.get('content', '') for msg in conversations if msg.get('role') == 'user']
                if user_messages:
                    # Get a few recent topics
                    recent_topics = user_messages[-3:] if len(user_messages) >= 3 else user_messages
                    topics_text = ', '.join([f'"{topic}"' for topic in recent_topics])
                    return f"We've been talking about: {topics_text}"
                else:
                    return "I can see we've had conversations, but I can't access the specific content right now."

        except Exception as e:
            print(f"❌ Conversation history error: {e}")
            return "Sorry, I had trouble accessing our conversation history."

def main():
    """Main chat loop with Mem0-style memory integration"""
    nova = NovaAI()

    while True:
        try:
            user_input = input("\n💬 You: ").strip()

            if not user_input:
                continue

            if user_input.lower() in ['quit', 'exit', 'bye']:
                # End current session
                try:
                    if hasattr(nova.memory, 'end_session'):
                        nova.memory.end_session()
                except Exception as e:
                    print(f"Debug: Session cleanup error - {e}")

                user_name = nova.get_user_profile().get('user_info', {}).get('name') or nova.get_user_profile().get('facts', {}).get('name', 'there')
                print(f"\n🌟 Nova: Take care, {user_name}! I've saved everything we talked about.")
                break

            response = nova.chat(user_input)
            if response:  # Only print if there's a response (commands return None)
                print(f"\n🌟 Nova: {response}")

        except KeyboardInterrupt:
            print("\n\n🌟 Nova: Bye! Everything is saved.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()

